// Webhook handler for video generation
import {
  VideoGenerationRequest,
  VideoStatusRequest,
  VideoGenerationResponse,
  VideoServiceResponse
} from '../types';
import { API_CONFIG } from '../config/api';

// Re-export types for backward compatibility
export type { VideoGenerationRequest, VideoStatusRequest, VideoGenerationResponse, VideoServiceResponse };

export class VideoGenerationWebhook {
  private static readonly WEBHOOK_URL = API_CONFIG.WEBHOOK_URL;

  /**
   * Initiate video generation using new schema
   */
  static async generateVideo(prompt: string, jobId: string): Promise<VideoServiceResponse> {
    try {
      const request: VideoGenerationRequest = {
        type: 'request',
        jobId,
        prompt,
        timestamp: new Date().toISOString(),
      };

      console.log('Sending request:', request);

      const response = await fetch(this.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'ngrok-skip-browser-warning': 'true', // For ngrok development
        },
        body: JSON.stringify(request),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      // Get response as text first to debug
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      let result: VideoGenerationResponse;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}...`);
      }

      console.log('Parsed API Response:', result);

      // Normalize status to lowercase for consistency
      const normalizedStatus = result.status.toLowerCase() as 'processing' | 'complete' | 'error';

      return {
        success: true,
        status: normalizedStatus,
        videoUrl: result.video_url || undefined,
        jobId,
      };

    } catch (error) {
      console.error('Video generation failed:', error);
      return {
        success: false,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        jobId,
      };
    }
  }



  /**
   * Check video generation status using new schema
   */
  static async checkStatus(jobId: string): Promise<VideoServiceResponse> {
    try {
      const request: VideoStatusRequest = {
        type: 'status',
        jobId,
      };

      console.log('Sending status request:', request);

      const response = await fetch(this.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'ngrok-skip-browser-warning': 'true',
        },
        body: JSON.stringify(request),
      });

      console.log('Status response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Status error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);
      }

      // Get response as text first to debug
      const responseText = await response.text();
      console.log('Raw status response:', responseText);

      if (!responseText.trim()) {
        throw new Error('Empty response from server');
      }

      let result: VideoGenerationResponse;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Status JSON parse error:', parseError);
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 200)}...`);
      }

      console.log('Parsed status response:', result);

      // Normalize status to lowercase for consistency
      const normalizedStatus = result.status.toLowerCase() as 'processing' | 'complete' | 'error';

      return {
        success: true,
        status: normalizedStatus,
        videoUrl: result.video_url || undefined,
        jobId,
      };
    } catch (error) {
      console.error('Status check failed:', error);
      return {
        success: false,
        status: 'error',
        error: error instanceof Error ? error.message : 'Status check failed',
        jobId,
      };
    }
  }

  /**
   * Poll for video completion with automatic retries and progress callback
   */
  static async pollForCompletion(
    jobId: string,
    maxAttempts: number = 30,
    intervalMs: number = 2000,
    onProgress?: (current: number, total: number, message: string) => void
  ): Promise<VideoServiceResponse> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      const result = await this.checkStatus(jobId);

      // Update progress
      onProgress?.(
        attempts + 1,
        maxAttempts,
        result.success && result.status === 'processing'
          ? 'Generating your video...'
          : 'Checking status...'
      );

      if (!result.success) {
        return result; // Return error immediately
      }

      if (result.status === 'complete' || result.status === 'error') {
        return result; // Return final result
      }

      // Still processing, wait and try again
      await new Promise(resolve => setTimeout(resolve, intervalMs));
      attempts++;
    }

    // Timeout reached
    return {
      success: false,
      status: 'error',
      error: 'Polling timeout: Video generation took too long',
      jobId,
    };
  }
}

// Example webhook payload structures for your backend

// Initial Request Example
export const exampleInitialRequest: VideoGenerationRequest = {
  type: "request",
  jobId: "job_1234567890_abc123def",
  prompt: "A majestic eagle soaring over snow-capped mountains at sunset",
  timestamp: "2024-01-15T10:30:00.000Z"
};

// Status Check Request Example
export const exampleStatusRequest: VideoStatusRequest = {
  type: "status",
  jobId: "job_1234567890_abc123def"
};

// Response Examples
export const exampleProcessingResponse: VideoGenerationResponse = {
  status: "processing",
  video_url: ""
};

export const exampleCompleteResponse: VideoGenerationResponse = {
  status: "complete",
  video_url: "https://example.com/generated-video.mp4"
};

export const exampleErrorResponse: VideoGenerationResponse = {
  status: "error",
  video_url: ""
};