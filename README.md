# Veo3 Video Generator

A modern React application for generating AI videos from text prompts using the Veo3 API.

## Features

- 🎥 AI-powered video generation from text prompts
- 🎮 Custom video player with full controls
- 🎨 Beautiful glassmorphism UI design
- 📱 Responsive design
- ⚡ Built with modern technologies

## Tech Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **API**: Custom webhook integration

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:

```bash
git clone <your-repo-url>
cd veo3
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
```

4. Edit `.env` and add your configuration:

```env
VITE_VIDEO_GENERATION_WEBHOOK_URL=your_webhook_url_here
VITE_API_KEY=your_api_key_here
```

5. Start the development server:

```bash
npm run dev
```

6. Open [http://localhost:5173](http://localhost:5173) in your browser.

## Project Structure

```
src/
├── api/           # API services and webhook handlers
├── components/    # Reusable React components
├── config/        # Configuration files
├── types/         # TypeScript type definitions
├── App.tsx        # Main application component
└── main.tsx       # Application entry point
```

## Environment Variables

| Variable                            | Description                  | Required |
| ----------------------------------- | ---------------------------- | -------- |
| `VITE_VIDEO_GENERATION_WEBHOOK_URL` | URL for video generation API | Yes      |
| `VITE_API_KEY`                      | API key for authentication   | No       |
| `VITE_APP_NAME`                     | Application name             | No       |
| `VITE_APP_DESCRIPTION`              | Application description      | No       |

## API Schema

The application uses a specific API schema for video generation:

### Initial Request

```json
{
  "type": "request",
  "jobId": "unique-job-identifier",
  "prompt": "user's video description",
  "timestamp": "ISO 8601 timestamp"
}
```

### Status Check Request

```json
{
  "type": "status",
  "jobId": "unique-job-identifier"
}
```

### Response Format

```json
{
  "status": "IN_QUEUE|COMPLETED|ERROR",
  "video_url": "https://example.com/video.mp4 or empty string"
}
```

### Example Responses

**In Queue (Processing):**

```json
{
  "status": "IN_QUEUE",
  "video_url": ""
}
```

**Completed:**

```json
{
  "status": "COMPLETED",
  "video_url": "https://example.com/generated-video.mp4"
}
```

**Error:**

```json
{
  "status": "ERROR",
  "video_url": ""
}
```

## Features

- **Automatic Polling**: The app automatically polls for video completion
- **Progress Tracking**: Real-time progress updates during generation
- **Error Handling**: Comprehensive error handling and user feedback
- **Timeout Protection**: Prevents infinite polling with configurable timeouts

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## License

This project is licensed under the MIT License.
