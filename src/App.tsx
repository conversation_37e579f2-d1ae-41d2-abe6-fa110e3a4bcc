import React, { useState, useRef, useEffect } from 'react';
import { Send, <PERSON>, <PERSON>rk<PERSON>, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { VideoPlayer } from './components/VideoPlayer';
import { VideoGenerationWebhook } from './api/webhook';
import { VideoGeneration } from './types';



function App() {
  const [prompt, setPrompt] = useState('');
  const [generations, setGenerations] = useState<VideoGeneration[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isTestingPolling, setIsTestingPolling] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [generations]);

  // Test polling function
  const testPolling = async () => {
    setIsTestingPolling(true);

    try {
      const testJobId = `test_job_${Date.now()}`;
      console.log('🧪 Testing polling with jobId:', testJobId);

      // Test direct status check
      const statusResult = await VideoGenerationWebhook.checkStatus(testJobId);
      console.log('🧪 Status check result:', statusResult);

      // Test polling function
      console.log('🧪 Starting polling test...');
      const pollResult = await VideoGenerationWebhook.pollForCompletion(
        testJobId,
        5, // Only 5 attempts for testing
        1000, // 1 second intervals for faster testing
        (current, total, message) => {
          console.log(`🧪 Polling progress: ${current}/${total} - ${message}`);
        }
      );

      console.log('🧪 Final polling result:', pollResult);

    } catch (error) {
      console.error('🧪 Polling test failed:', error);
    } finally {
      setIsTestingPolling(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isGenerating) return;

    const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const newGeneration: VideoGeneration = {
      id: Date.now().toString(),
      prompt: prompt.trim(),
      status: 'IN_QUEUE',
      timestamp: new Date(),
      jobId,
    };

    setGenerations(prev => [...prev, newGeneration]);
    setIsGenerating(true);
    setPrompt('');

    try {
      // Step 1: Initiate video generation
      const initResult = await VideoGenerationWebhook.generateVideo(
        newGeneration.prompt,
        jobId
      );

      if (!initResult.success) {
        throw new Error(initResult.error || 'Failed to initiate video generation');
      }

      console.log('Video generation initiated:', initResult);

      // Check if video is already complete
      if (initResult.status === 'COMPLETED' && initResult.videoUrl) {
        console.log('Video generation completed immediately:', initResult);

        // Update the generation with the video URL
        setGenerations(prev =>
          prev.map(gen =>
            gen.id === newGeneration.id
              ? {
                  ...gen,
                  status: 'COMPLETED',
                  videoUrl: initResult.videoUrl,
                  duration: 10, // Default duration
                }
              : gen
          )
        );
      } else if (initResult.status === 'IN_QUEUE') {
        // Step 2: Poll for completion with progress updates (new schema)
        const finalResult = await VideoGenerationWebhook.pollForCompletion(
          jobId,
          30, // max attempts
          2000, // interval ms
          (current, total, message) => {
            // Update progress in real-time
            setGenerations(prev =>
              prev.map(gen =>
                gen.id === newGeneration.id
                  ? {
                      ...gen,
                      progress: { current, total, message }
                    }
                  : gen
              )
            );
          }
        );

        if (finalResult.success && finalResult.status === 'COMPLETED' && finalResult.videoUrl) {
          console.log('Video generation completed after polling:', finalResult);

          // Update the generation with the video URL
          setGenerations(prev =>
            prev.map(gen =>
              gen.id === newGeneration.id
                ? {
                    ...gen,
                    status: 'COMPLETED',
                    videoUrl: finalResult.videoUrl,
                    duration: 10, // Default duration
                  }
                : gen
            )
          );
        } else {
          throw new Error(finalResult.error || 'Video generation failed or timed out');
        }
      } else if (initResult.status === 'ERROR') {
        throw new Error('Video generation failed with error status');
      } else {
        throw new Error('Unexpected response status: ' + initResult.status);
      }
    } catch (error) {
      console.error('Video generation failed:', error);
      setGenerations(prev =>
        prev.map(gen =>
          gen.id === newGeneration.id
            ? {
                ...gen,
                status: 'ERROR'
              }
            : gen
        )
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusIcon = (status: VideoGeneration['status']) => {
    switch (status) {
      case 'IN_QUEUE':
        return <Clock className="w-4 h-4 animate-spin text-yellow-400" />;
      case 'COMPLETED':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'ERROR':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
    }
  };

  const getStatusText = (status: VideoGeneration['status']) => {
    switch (status) {
      case 'IN_QUEUE':
        return 'Generating your video...';
      case 'COMPLETED':
        return 'Video generated successfully!';
      case 'ERROR':
        return 'Generation failed. Please try again.';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/10 bg-black/20 backdrop-blur-xl">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl">
                <Video className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Veo3 Video Generator</h1>
                <p className="text-sm text-gray-400">Create stunning videos with AI</p>
              </div>
            </div>

            {/* Test Polling Button */}
            <button
              onClick={testPolling}
              disabled={isTestingPolling}
              className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed text-white text-sm rounded-lg transition-colors duration-200 flex items-center space-x-2"
            >
              {isTestingPolling ? (
                <>
                  <Clock className="w-4 h-4 animate-spin" />
                  <span>Testing...</span>
                </>
              ) : (
                <>
                  <span>🧪</span>
                  <span>Test Polling</span>
                </>
              )}
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-6 py-8 flex flex-col h-[calc(100vh-100px)]">
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto mb-6 space-y-6">
          {generations.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center py-12">
              <div className="p-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full mb-6">
                <Sparkles className="w-12 h-12 text-purple-400" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-3">
                Welcome to Veo3 Video Generator
              </h2>
              <p className="text-gray-400 max-w-md">
                Describe the video you want to create and watch AI bring your vision to life.
                Start by typing a prompt below.
              </p>
            </div>
          ) : (
            generations.map((generation) => (
              <div
                key={generation.id}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
                    <Video className="w-5 h-5 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      {getStatusIcon(generation.status)}
                      <span className="text-sm font-medium text-gray-300">
                        {getStatusText(generation.status)}
                      </span>
                    </div>
                    <p className="text-white text-lg mb-4 leading-relaxed">
                      "{generation.prompt}"
                    </p>
                    
                    {generation.status === 'COMPLETED' && generation.videoUrl && (
                      <div className="bg-black/30 rounded-xl p-4 border border-white/10">
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm text-gray-400">Generated Video</span>
                          <span className="text-xs text-gray-500">
                            {generation.duration}s
                          </span>
                        </div>
                        <VideoPlayer
                          src={generation.videoUrl}
                          className="h-64"
                          onLoad={() => console.log('Video loaded')}
                        />
                      </div>
                    )}
                    
                    {generation.status === 'IN_QUEUE' && (
                      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl p-4 border border-purple-500/20">
                        <div className="flex items-center space-x-3">
                          <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                          <div className="flex-1 bg-white/10 rounded-full h-2 overflow-hidden">
                            <div
                              className="bg-gradient-to-r from-purple-500 to-pink-500 h-full rounded-full transition-all duration-300"
                              style={{
                                width: generation.progress
                                  ? `${(generation.progress.current / generation.progress.total) * 100}%`
                                  : '33%'
                              }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-400">
                            {generation.progress?.message || 'Generating...'}
                          </span>
                        </div>
                        {generation.progress && (
                          <div className="mt-2 text-xs text-gray-500 text-center">
                            Step {generation.progress.current} of {generation.progress.total}
                          </div>
                        )}
                      </div>
                    )}
                    
                    <div className="mt-3 text-xs text-gray-500">
                      {generation.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input Area */}
        <div className="relative">
          <form onSubmit={handleSubmit} className="relative">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-2 focus-within:border-purple-500/50 transition-all duration-300">
              <div className="flex items-end space-x-3">
                <div className="flex-1">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Describe the video you want to generate... (e.g., 'A majestic eagle soaring over snow-capped mountains at sunset')"
                    className="w-full bg-transparent text-white placeholder-gray-400 resize-none border-0 focus:ring-0 focus:outline-none p-3 text-lg min-h-[60px] max-h-32"
                    rows={2}
                    disabled={isGenerating}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSubmit(e);
                      }
                    }}
                  />
                </div>
                <button
                  type="submit"
                  disabled={!prompt.trim() || isGenerating}
                  className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl text-white hover:from-purple-600 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 active:scale-95 shadow-lg hover:shadow-purple-500/25"
                >
                  {isGenerating ? (
                    <Clock className="w-5 h-5 animate-spin" />
                  ) : (
                    <Send className="w-5 h-5" />
                  )}
                </button>
              </div>
            </div>
          </form>
          
          <div className="flex items-center justify-between mt-3 px-2">
            <p className="text-xs text-gray-500">
              Press Enter to send, Shift+Enter for new line
            </p>
            <div className="flex items-center space-x-2 text-xs text-gray-500">
              <span>Powered by AI</span>
              <Sparkles className="w-3 h-3" />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default App;