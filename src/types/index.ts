// Video Generation Types
export interface VideoGeneration {
  id: string;
  prompt: string;
  status: 'generating' | 'completed' | 'failed';
  videoUrl?: string;
  timestamp: Date;
  duration?: number;
  jobId?: string;
}

// API Request/Response Types
export interface VideoGenerationRequest {
  prompt: string;
  id: string;
  userId?: string;
  options?: {
    duration?: number;
    quality?: 'standard' | 'high' | 'premium';
    aspectRatio?: '16:9' | '9:16' | '1:1';
  };
}

export interface VideoGenerationResponse {
  success: boolean;
  videoUrl?: string;
  duration?: number;
  error?: string;
  jobId?: string;
}

// Component Props Types
export interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  onLoad?: () => void;
}
