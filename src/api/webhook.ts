// Webhook handler for video generation
import { VideoGenerationRequest, VideoGenerationResponse } from '../types';
import { API_CONFIG } from '../config/api';

// Re-export types for backward compatibility
export type { VideoGenerationRequest, VideoGenerationResponse };

export class VideoGenerationWebhook {
  private static readonly WEBHOOK_URL = API_CONFIG.WEBHOOK_URL;

  static async generateVideo(request: VideoGenerationRequest): Promise<VideoGenerationResponse> {
    try {
      const response = await fetch(this.WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'ngrok-skip-browser-warning': 'true', // For ngrok development
        },
        body: JSON.stringify({
          ...request,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Handle different response formats
      return {
        success: true,
        videoUrl: result.videoUrl || result.url || result.video_url,
        duration: result.duration || 10,
        jobId: result.jobId || result.job_id,
      };
    } catch (error) {
      console.error('Video generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  static async checkStatus(jobId: string): Promise<VideoGenerationResponse> {
    try {
      const response = await fetch(`${this.WEBHOOK_URL}/status/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${API_CONFIG.API_KEY}`,
          'ngrok-skip-browser-warning': 'true',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return {
        success: result.success !== false,
        videoUrl: result.videoUrl || result.url || result.video_url,
        duration: result.duration,
        jobId: result.jobId || result.job_id,
        error: result.error,
      };
    } catch (error) {
      console.error('Status check failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Status check failed',
      };
    }
  }
}

// Example webhook payload structure for your backend
export const exampleWebhookPayload = {
  prompt: "A majestic eagle soaring over snow-capped mountains at sunset",
  id: "gen_1234567890",
  userId: "user_abc123",
  timestamp: "2024-01-15T10:30:00.000Z",
  options: {
    duration: 10,
    quality: "high",
    aspectRatio: "16:9",
  }
};

// Example response structure your webhook should return
export const exampleWebhookResponse = {
  success: true,
  jobId: "job_9876543210",
  estimatedDuration: 180, // seconds until completion
  videoUrl: null, // will be populated when complete
};