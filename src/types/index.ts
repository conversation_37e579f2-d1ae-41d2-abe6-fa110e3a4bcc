// Video Generation Types
export interface VideoGeneration {
  id: string;
  prompt: string;
  status: 'PROCESSING' | 'COMPLETED' | 'ERROR';
  videoUrl?: string;
  timestamp: Date;
  duration?: number;
  jobId?: string;
  progress?: {
    current: number;
    total: number;
    message?: string;
  };
}

// API Request/Response Types
export interface VideoGenerationRequest {
  type: 'request';
  jobId: string;
  prompt: string;
  timestamp: string;
}

export interface VideoStatusRequest {
  type: 'status';
  jobId: string;
}

export interface VideoGenerationResponse {
  status: 'PROCESSING' | 'COMPLETED' | 'ERROR';
  video_url: string;
}

// Internal service response type for better handling
export interface VideoServiceResponse {
  success: boolean;
  status: 'PROCESSING' | 'COMPLETED' | 'ERROR';
  videoUrl?: string;
  error?: string;
  jobId: string;
}

// Component Props Types
export interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  onLoad?: () => void;
}
